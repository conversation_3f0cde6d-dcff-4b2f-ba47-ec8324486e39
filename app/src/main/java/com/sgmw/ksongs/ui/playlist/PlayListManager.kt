package com.sgmw.ksongs.ui.playlist

import androidx.lifecycle.LiveData
import com.sgmw.common.utils.DaemonThreadDispatcher
import com.sgmw.common.utils.Log
import com.sgmw.common.utils.ioLaunch
import com.sgmw.common.utils.mainLaunch
import com.sgmw.ksongs.db.DbManager
import com.sgmw.ksongs.db.dao.DemandDao
import com.sgmw.ksongs.db.entity.DemandSongInfo
import com.sgmw.ksongs.model.bean.SongInfoBean
import com.sgmw.ksongs.track.SensorsDataManager
import com.sgmw.ksongs.ui.songplay.KaraokeConsole
import com.sgmw.ksongs.utils.addPlayRecord
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.sync.Mutex

/**
 * @author: 董俊帅
 * @time: 2025/2/20
 * @desc: 播放列表管理类
 */
object PlayListManager {

    private const val TAG = "PlayListManager"

    /**
     * 获取DemandDao，每次都从DbManager获取最新实例，确保数据库重新初始化后能正常工作
     */
    private fun getDemandDao(): DemandDao {
        return DbManager.getDemandSongInfoDao()
    }

    // 使用更细粒度的锁，避免所有操作都串行化
    private val updatePlayingMutex = Mutex()
    private val updateSungMutex = Mutex()

    // 操作状态标记，用于快速检查和避免重复操作
    @Volatile
    private var isUpdatingPlaying = false
    @Volatile
    private var isUpdatingSung = false

    private val _playRecordUpdated = MutableSharedFlow<Unit>()
    val playRecordUpdated: Flow<Unit> = _playRecordUpdated.asSharedFlow()

    /**
     * 添加歌曲到已点列表， 已添加的不再添加
     */
    fun addDemandSongInfo(demandSongInfo: DemandSongInfo, isPlayEnd: Boolean = false, cardName: String = "") {
        ioLaunch {
            try {
                val demandDao = getDemandDao()

                // 添加数据验证
                val songInfo = demandSongInfo.songInfo
                if (songInfo.song_name.isBlank() || songInfo.singer_name.isBlank()) {
                    Log.e(TAG, "尝试添加无效歌曲数据: song_id=${songInfo.song_id}, song_name='${songInfo.song_name}', singer_name='${songInfo.singer_name}'")
                    return@ioLaunch // 不添加无效数据
                }

                if (!demandDao.isSongIdExists(demandSongInfo.songInfo.song_id)) {
                    if (!isPlayEnd) {
                        demandSongInfo.songInfo.isPlaying = false
                        demandSongInfo.songInfo.isPlayingState = false
                    }
                    demandDao.insertDemandSongInfo(demandSongInfo)
                    SungListManager.removeFromSungList(demandSongInfo.songInfo)
                    // 下方是埋点
                    SensorsDataManager.trackAddDemandClickEvent(cardName, demandSongInfo.songInfo.song_name, demandSongInfo.songInfo.singer_name)
                } else {
                    demandDao.deleteDemandSongInfo(demandSongInfo.songInfo.song_id)
                }
            } catch (e: Exception) {
                Log.e(TAG, "addDemandSongInfo failed for ${demandSongInfo.songInfo.song_name}", e)
            }
        }
    }

    /**
     * 获取当前已点列表中的所有歌曲
     */
    suspend fun getDemandSongInfoList(): MutableList<DemandSongInfo> {
        return getDemandDao().getAllDemandSongInfo()
    }

    /**
     * 获取当前已点列表中的所有歌曲除去正在播放的歌曲
     */
    suspend fun getDemandSongInfoListWithOutPlaying(): MutableList<DemandSongInfo> {
        return getDemandDao().getAllDemandSongInfoWithOutPlaying()
    }

    /**
     * 获取正在播放的歌曲
     */
    fun getPlayingSongInfoLiveData(): LiveData<DemandSongInfo?> {
        return getDemandDao().getPlayingSongInfoLiveData()
    }

    /**
     * 获取正在播放的歌曲
     */
    suspend fun getPlayingSongInfo(): DemandSongInfo? {
        return getDemandDao().getPlayingSongInfo()
    }

    /**
     * 将歌曲添加到已点列表的顶部
     */
    suspend fun addTopSongInfo(songInfo: DemandSongInfo) {
        // 添加数据验证
        val songInfoBean = songInfo.songInfo
        if (songInfoBean.song_name.isNullOrBlank() || songInfoBean.singer_name.isNullOrBlank()) {
            Log.e(TAG, "尝试添加无效歌曲数据到顶部: song_id=${songInfoBean.song_id}, song_name='${songInfoBean.song_name}', singer_name='${songInfoBean.singer_name}'")
            return // 不添加无效数据
        }

        val demandDao = getDemandDao()
        val topDemandSongInfo = demandDao.getTopDemandSongInfo()
        val currentTime = topDemandSongInfo?.insertTime ?: System.currentTimeMillis()
        val songInfoById = demandDao.getSongInfoById(songInfo.songInfo.song_id)
        if (songInfoById != null) {
            demandDao.deleteDemandSongInfo(songInfoById)
        }
        songInfo.insertTime = currentTime - 1
        demandDao.insertDemandSongInfo(songInfo)
        SungListManager.removeFromSungList(songInfo.songInfo)
    }

    /**
     * 删除所有的已点歌曲
     */
    suspend fun deleteAllDemandSongInfo() {
        getDemandDao().deleteAllDemandSongInfo()
    }

    /**
     * 获取下一首要播放的歌曲信息
     */
    suspend fun getNextPlaySongInfo(): DemandSongInfo? {
        return try {
            // 使用数据库专用线程池，避免阻塞主线程
            withContext(DaemonThreadDispatcher.Database) {
                val songInfoList = getDemandDao().getAllDemandSongInfoWithOutPlaying()
                if (songInfoList.isEmpty()) {
                    null
                } else {
                    val songInfo = songInfoList[0]
                    Log.d(TAG, "getNextPlaySongInfo song_name: ${songInfo.songInfo.song_name}")
                    songInfo
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "getNextPlaySongInfo error: ${e.message}", e)
            null
        }
    }

    /**
     * 删除已点歌曲
     */
    suspend fun deleteDemandSongInfoById(songId: String) {
        try {
            // 使用数据库专用线程池，避免阻塞主线程
            withContext(DaemonThreadDispatcher.Database) {
                getDemandDao().deleteDemandSongInfo(songId)
            }
        } catch (e: Exception) {
            Log.e(TAG, "deleteDemandSongInfoById error: ${e.message}", e)
        }
    }

    /**
     * 随机打乱已点列表
     */
    suspend fun shuffleDemandList() {
        val list = getDemandSongInfoListWithOutPlaying()
        list.shuffle()
        val baseTime = System.currentTimeMillis()
        for (i in list.indices) {
            val songInfoBean = list[i]
            // 为每个歌曲设置不同的时间戳，确保排序稳定
            songInfoBean.insertTime = baseTime + i
        }
        getDemandDao().update(list)
    }

    /**
     * 获取已点歌曲列表LiveData
     */
    fun getAllDemandSongInfoLiveData(): LiveData<MutableList<DemandSongInfo>> {
        return getDemandDao().getAllDemandSongInfoLiveData()
    }

    /**
     * 获取已点歌曲列表LiveData，包含正在播放的歌曲
     */
    fun getAllDemandSongInfoWithPlayingLiveData(): LiveData<MutableList<DemandSongInfo>> {
        return getDemandDao().getAllDemandSongInfoWithPlayingLiveData()
    }

    /**
     * 把当前歌曲设置成正在播放的歌曲
     * 优化版本：使用细粒度锁和快速检查，避免ANR和死锁
     */
    suspend fun updatePlayingSongInfo(songInfo: SongInfoBean) {
        // 快速检查，避免重复操作
        if (isUpdatingPlaying) {
            Log.d(TAG, "updatePlayingSongInfo: 正在处理中，跳过重复请求")
            return
        }

        // 数据验证
        if (songInfo.song_name.isNullOrBlank() || songInfo.singer_name.isNullOrBlank()) {
            Log.e(TAG, "尝试设置无效歌曲为正在播放: song_id=${songInfo.song_id}, song_name='${songInfo.song_name}', singer_name='${songInfo.singer_name}'")
            return
        }

        // 使用专用线程池，避免在主线程执行耗时操作
        withContext(DaemonThreadDispatcher.Database) {
            // 使用tryLock避免长时间等待，如果获取不到锁就跳过
            if (!updatePlayingMutex.tryLock()) {
                Log.d(TAG, "updatePlayingSongInfo: 无法获取锁，可能有其他操作正在进行")
                return@withContext
            }

            try {
                isUpdatingPlaying = true
                Log.d(TAG, "updatePlayingSongInfo: 开始设置正在播放歌曲 ${songInfo.song_name}")

                val demandDao = getDemandDao()

                // 使用数据库事务确保原子性
                DbManager.runInTransaction {
                    // 1. 先处理当前正在播放的歌曲
                    val currentPlayingSong = demandDao.getPlayingSongInfo()
                    currentPlayingSong?.let { playingSong ->
                        Log.d(TAG, "updatePlayingSongInfo: 当前播放歌曲 ${playingSong.songInfo.song_name}")

                        // 只有当新歌曲与当前播放歌曲不同时才处理
                        if (songInfo.song_id != playingSong.songInfo.song_id) {
                            Log.d(TAG, "updatePlayingSongInfo: 处理旧的播放歌曲，移动到已唱列表")

                            // 设置为非播放状态
                            playingSong.songInfo.isPlaying = false

                            // 从已点列表删除
                            demandDao.deleteDemandSongInfo(playingSong)

                            // 添加到已唱歌曲
                            SungListManager.addToSungList(playingSong.songInfo)

                            // 添加到播放历史记录
                            addPlayRecord(playingSong.songInfo)
                        } else {
                            Log.d(TAG, "updatePlayingSongInfo: 相同歌曲，无需处理")
                            return@runInTransaction
                        }
                    }

                    // 2. 处理新的播放歌曲
                    Log.d(TAG, "updatePlayingSongInfo: 设置新歌曲为正在播放")

                    // 从已唱列表移除（如果存在）
                    SungListManager.removeFromSungList(songInfo)

                    // 检查新歌曲是否已在已点列表中
                    var newPlayingSong = demandDao.getSongInfoById(songInfo.song_id)
                    if (newPlayingSong != null) {
                        // 如果已存在，先删除再重新插入（确保状态正确）
                        demandDao.deleteDemandSongInfo(newPlayingSong)
                        newPlayingSong.songInfo.isPlaying = true
                    } else {
                        // 如果不存在，创建新的记录
                        songInfo.isPlaying = true
                        newPlayingSong = DemandSongInfo(songInfo = songInfo)
                    }

                    // 插入新的正在播放歌曲
                    demandDao.insertDemandSongInfo(newPlayingSong)

                    Log.d(TAG, "updatePlayingSongInfo: 完成设置正在播放歌曲 ${songInfo.song_name}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "updatePlayingSongInfo 执行失败: ${e.message}", e)
            } finally {
                isUpdatingPlaying = false
                updatePlayingMutex.unlock()
            }
        }
    }

    /**
     * 把当前歌曲设置成已经播放的歌曲
     * 优化版本：使用细粒度锁和快速检查，避免ANR和死锁
     */
    fun updateSungList(mid: String) {
        ioLaunch {
            // 快速检查，避免重复操作
            if (isUpdatingSung) {
                Log.d(TAG, "updateSungList: 正在处理中，跳过重复请求")
                return@ioLaunch
            }

            // 使用专用线程池，确保不在主线程执行
            withContext(DaemonThreadDispatcher.Database) {
                // 使用tryLock避免长时间等待
                if (!updateSungMutex.tryLock()) {
                    Log.d(TAG, "updateSungList: 无法获取锁，可能有其他操作正在进行")
                    return@withContext
                }

                try {
                    isUpdatingSung = true
                    Log.d(TAG, "updateSungList: 开始处理歌曲 $mid")

                    val demandDao = getDemandDao()

                    // 使用数据库事务确保原子性
                    DbManager.runInTransaction {
                        // 1. 数据一致性检查：确保歌曲存在且状态正确
                        val songInfoById = demandDao.getSongInfoById(mid)
                        if (songInfoById == null) {
                            Log.w(TAG, "updateSungList: 歌曲 $mid 不存在于已点列表中，可能已被处理")
                            return@runInTransaction
                        }

                        Log.d(TAG, "updateSungList: 找到歌曲 ${songInfoById.songInfo.song_name}")

                        // 2. 验证歌曲状态，确保数据一致性
                        if (!songInfoById.songInfo.isPlaying) {
                            Log.w(TAG, "updateSungList: 歌曲 ${songInfoById.songInfo.song_name} 不是正在播放状态，可能存在状态不一致")
                        }

                        // 3. 在主线程更新UI状态（必须在删除数据库记录之前）
                        mainLaunch {
                            // 清空当前播放信息
                            KaraokeConsole.currSongInfo = null
                            KaraokeConsole.currSongInfoLiveData.value = null
                        }

                        // 4. 按顺序执行数据库操作（在事务中确保原子性）
                        Log.d(TAG, "updateSungList: 从已点列表删除歌曲")
                        demandDao.deleteDemandSongInfo(songInfoById)

                        Log.d(TAG, "updateSungList: 添加到已唱列表")
                        SungListManager.addToSungList(songInfoById.songInfo)

                        Log.d(TAG, "updateSungList: 添加播放记录")
                        addPlayRecord(songInfoById.songInfo)

                        Log.d(TAG, "updateSungList: 完成处理歌曲 ${songInfoById.songInfo.song_name}")
                    }

                    // 5. 发送通知
                    _playRecordUpdated.emit(Unit)

                } catch (e: Exception) {
                    Log.e(TAG, "updateSungList 执行失败: ${e.message}", e)
                } finally {
                    isUpdatingSung = false
                    updateSungMutex.unlock()
                }
            }
        }
    }

    /**
     * 数据一致性检查和修复
     * 优化版本：使用超时机制和轻量级检查，避免启动时阻塞过久
     */
    suspend fun validateAndRepairDataConsistency() {
        // 使用专用线程池，避免阻塞主线程
        withContext(DaemonThreadDispatcher.Database) {
            try {
                Log.d(TAG, "validateAndRepairDataConsistency: 开始数据一致性检查")

                val demandDao = getDemandDao()

                // 使用超时机制，避免长时间阻塞
                withTimeout(5000) { // 5秒超时
                    DbManager.runInTransaction {
                        // 1. 快速检查是否有多个正在播放的歌曲
                        val playingSongs = demandDao.getAllDemandSongInfo().filter { it.songInfo.isPlaying }

                        if (playingSongs.size > 1) {
                            Log.w(TAG, "validateAndRepairDataConsistency: 发现 ${playingSongs.size} 个正在播放的歌曲，进行修复")

                            // 保留最新的一个，其他的设为非播放状态
                            val latestPlayingSong = playingSongs.maxByOrNull { it.insertTime }
                            playingSongs.forEach { song ->
                                if (song.demandId != latestPlayingSong?.demandId) {
                                    song.songInfo.isPlaying = false
                                    demandDao.insertDemandSongInfo(song)
                                    Log.d(TAG, "validateAndRepairDataConsistency: 修复歌曲 ${song.songInfo.song_name} 的播放状态")
                                }
                            }
                        }

                        // 2. 轻量级数据完整性检查
                        val totalSongs = demandDao.getAllDemandSongInfo().size
                        Log.d(TAG, "validateAndRepairDataConsistency: 总歌曲数=$totalSongs, 播放歌曲数=${playingSongs.size}")

                        // 3. 快速清理明显无效的数据（只检查关键字段）
                        val invalidSongs = demandDao.getAllDemandSongInfo().filter {
                            it.songInfo.song_id.isBlank() || it.songInfo.song_name.isBlank()
                        }

                        if (invalidSongs.isNotEmpty()) {
                            Log.w(TAG, "validateAndRepairDataConsistency: 发现 ${invalidSongs.size} 首无效歌曲，进行清理")
                            invalidSongs.forEach { invalidSong ->
                                demandDao.deleteDemandSongInfo(invalidSong)
                                Log.d(TAG, "validateAndRepairDataConsistency: 删除无效歌曲 song_id=${invalidSong.songInfo.song_id}")
                            }
                        }

                        Log.d(TAG, "validateAndRepairDataConsistency: 数据一致性检查完成")
                    }
                }
            } catch (e: TimeoutCancellationException) {
                Log.w(TAG, "validateAndRepairDataConsistency: 检查超时，跳过本次检查")
            } catch (e: Exception) {
                Log.e(TAG, "validateAndRepairDataConsistency 执行失败: ${e.message}", e)
            }
        }
    }

}