package com.sgmw.ksongs

import android.annotation.SuppressLint
import android.car.hardware.power.CarPowerManager
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import androidx.navigation.findNavController
import androidx.navigation.fragment.NavHostFragment
import com.blankj.utilcode.util.ToastUtils
import com.sgmw.common.ktx.setOnSingleClickListener
import com.sgmw.common.mvvm.v.BaseFrameActivity
import com.sgmw.common.utils.Log
import com.sgmw.common.utils.RegisterEventBus
import com.sgmw.common.utils.ioLaunch
import com.sgmw.common.utils.network.NetworkStateClient
import com.sgmw.ksongs.application.MyApplication
import com.sgmw.ksongs.databinding.ActivityMainBinding
import com.sgmw.ksongs.db.DbManager
import com.sgmw.ksongs.manager.AudioFocusHelper
import com.sgmw.ksongs.manager.CarManager
import com.sgmw.ksongs.manager.PermissionHelper
import com.sgmw.ksongs.manager.StatusBarManager
import com.sgmw.ksongs.model.bean.LoginSuccessEvent
import com.sgmw.ksongs.model.bean.SongInfoBean
import com.sgmw.ksongs.model.bean.onFailure
import com.sgmw.ksongs.model.bean.onSuccess
import com.sgmw.ksongs.model.eventbus.PlayControlEvent
import com.sgmw.ksongs.model.repository.AccessTokenManager
import com.sgmw.ksongs.phonestatus.PhoneStatusManager
import com.sgmw.ksongs.receiver.LauncherToKSongsReceiver
import com.sgmw.ksongs.track.SensorsDataManager
import com.sgmw.ksongs.ui.dialog.ScoreDialogFragment
import com.sgmw.ksongs.ui.dialog.VehicleVideoDialogFragment
import com.sgmw.ksongs.ui.login.LoginFragment
import com.sgmw.ksongs.ui.playlist.PlayListManager
import com.sgmw.ksongs.ui.playlist.SongListDialogFragment
import com.sgmw.ksongs.ui.privacy.AgreePrivacyFragment
import com.sgmw.ksongs.ui.protocol.ProtocolHomeFragment
import com.sgmw.ksongs.ui.protocol.ProtocolWebFragment
import com.sgmw.ksongs.ui.settings.MicConnectionGuideFragment
import com.sgmw.ksongs.ui.settings.SettingsFragment
import com.sgmw.ksongs.ui.songplay.KaraokeConsole
import com.sgmw.ksongs.ui.songplay.KaraokePlayerManager
import com.sgmw.ksongs.ui.songplay.SongPlayController
import com.sgmw.ksongs.ui.songplay.VipAndLimitManager
import com.sgmw.ksongs.ui.vr.VrKSongPlayDialogFragment
import com.sgmw.ksongs.utils.ERROR_CODE_OTHER_LOGIN
import com.sgmw.ksongs.utils.ERROR_CODE_TOKEN_EXPIRE
import com.sgmw.ksongs.utils.LingOsDialogUtils
import com.sgmw.ksongs.utils.NavigationUtils
import com.sgmw.ksongs.utils.showToast
import com.sgmw.ksongs.viewmodel.MainViewModel
import com.sgmw.ksongs.vr.VrManager
import com.tme.ktv.player.PlayerManager
import com.tme.ktv.video.api.VideoState
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject


@RegisterEventBus
class MainActivity : BaseFrameActivity<ActivityMainBinding, MainViewModel>() {

    private var vrSongInfo: SongInfoBean? = null
    private var vrKsongData: String = String()

    private val mReceiver = LauncherToKSongsReceiver()
    private var songPlayController: SongPlayController? = null

    /** 埋点：浏览起始时间 */
    private var mStartTime = -1L

    /**
     * 是否要返回首页
     */
    private var needBackHome = false

    /**
     * 车机下电状态监听
     */
    private val carPowerStateListener = object : CarManager.CarPowerStateListener {
        override fun onStateChanged(state: Int) {
            Log.d(TAG, "Car power state changed: $state")
            if (state == CarPowerManager.STATE_SHUTDOWN_PREPARE
                || state == CarPowerManager.STATE_STANDBY
            ) {
                needBackHome = true
                // 车机准备关闭时，主动清理资源避免ANR
                prepareForShutdown()
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onPlayControlEvent(playControlEvent: PlayControlEvent) {
        Log.d(TAG, "onPlayControlEvent ${playControlEvent.visible}")
        mBinding.llPlayControl.visibility = playControlEvent.visible
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onLoginSuccessEvent(loginSuccessEvent: LoginSuccessEvent) {
        Log.d(TAG, "onLoginSuccessEvent: Login success, clearing fragment states")
        // 登录成功后，清理可能存在的无效Fragment状态
        clearInvalidFragmentStates()
    }

    /**
     * 清理无效的Fragment状态
     * 当登录成功后，清理由于replace操作导致的Fragment状态异常
     */
    private fun clearInvalidFragmentStates() {
        try {
            val navHostFragment = supportFragmentManager.findFragmentById(R.id.nav_host_fragment_activity_main)
            if (navHostFragment is NavHostFragment) {
                val fragmentManager = navHostFragment.childFragmentManager
                val fragments = fragmentManager.fragments.toList()

                Log.d(TAG, "clearInvalidFragmentStates: Found ${fragments.size} fragments")

                // 检查并清理状态异常的Fragment
                for (fragment in fragments) {
                    if (fragment != null && fragment !is LoginFragment) {
                        // 检查Fragment是否处于异常状态（View被销毁但对象还存在）
                        if (fragment.view == null && fragment.isAdded) {
                            Log.w(TAG, "clearInvalidFragmentStates: Found invalid fragment ${fragment.javaClass.simpleName}, removing it")
                            val transaction = fragmentManager.beginTransaction()
                            transaction.remove(fragment)
                            transaction.commitAllowingStateLoss()
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "clearInvalidFragmentStates: Error clearing invalid fragments", e)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        MyApplication.mainActivity = this
        PhoneStatusManager.registerPhoneStatusReceiver(application)
        PhoneStatusManager.initPhoneStatus(applicationContext)
        PermissionHelper.registerChangeListener(this@MainActivity)
        CarManager.registerCarPowerStateListener(carPowerStateListener)
        val filter = IntentFilter(LauncherToKSongsReceiver.CUSTOM_ACTION)
        registerReceiver(mReceiver, filter)

        // Initialize song play controller
        songPlayController = SongPlayController(this, mBinding.root, mBinding)

        //设置状态栏
        StatusBarManager.setUpStatusBar(false, this, mBinding)
        NavigationUtils.setNavController(findNavController(R.id.nav_host_fragment_activity_main))

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                val dialogFragmentShowing = songPlayController?.isDialogFragmentShowing(this@MainActivity)
                Log.d(TAG, "handleOnBackPressed dialogFragmentShowing: $dialogFragmentShowing")

                Log.d(TAG, "ScoreDialogFragment.hasNoNextScoreDialogFinish: ${ScoreDialogFragment.hasNoNextScoreDialogFinish}")
                // 解决在播放页面，先弹出了播放列表弹框，又弹出了打分弹框，关闭打分弹框后的回退问题
                if (ScoreDialogFragment.hasNoNextScoreDialogFinish) {
                    songPlayController?.apply {
                        dismissAllDialogFragment(this@MainActivity)
                        hide()
                        ScoreDialogFragment.hasNoNextScoreDialogFinish = false
                    }
                    return
                }

                if (dialogFragmentShowing == true) {
                    songPlayController?.dismissDialogFragment(this@MainActivity)
                    return
                }

                if (songPlayController?.isPlayViewShow() == true) {
                    songPlayController?.hide() // 拦截返回键
                } else {
                    val currentDestination = findNavController(R.id.nav_host_fragment_activity_main).currentDestination
                    if (currentDestination?.id == R.id.navigation_home) {
                        finish()
                    } else {
                        val findNavController = findNavController(R.id.nav_host_fragment_activity_main)
                        val backQueue = findNavController.backQueue
                        for (fragment in backQueue) {
                            Log.d(TAG, "backQueue: ${fragment.destination.label}")
                        }
                        Log.d(TAG, "currentDestination?.id: ${currentDestination?.id}")
                        isEnabled = false // 临时禁用回调
                        onBackPressedDispatcher.onBackPressed() // 执行默认返回
                        isEnabled = true // 恢复回调
                    }
                }
            }
        })
    }

    override fun onStart() {
        super.onStart()
        mStartTime = System.currentTimeMillis()
    }

    override fun onResume() {
        super.onResume()
        handleWalkLimit()
        songPlayController?.onResume()
    }

    override fun onPause() {
        super.onPause()
    }

    override fun onStop() {
        Log.d(TAG, "onStop")
        KaraokePlayerManager.pause()
        AudioFocusHelper.abandonAudioFocus()
        if (mStartTime > 0) {
            val mEndTime = System.currentTimeMillis() - mStartTime
            Log.d(TAG, "mEndTime: $mEndTime")
            SensorsDataManager.trackBrowseAppPage(mEndTime)
            mStartTime = -1L
        }
        super.onStop()
    }

    override fun initRequestData() {
        mViewModel?.getCurrentAndNextSongInfo()

        // 启动时进行数据一致性检查和修复
        ioLaunch {
            try {
                PlayListManager.validateAndRepairDataConsistency()
            } catch (e: Exception) {
                Log.e(TAG, "数据一致性检查失败: ${e.message}", e)
            }
        }
    }

    @SuppressLint("SetTextI18n")
    override fun initObserve() {
        mViewModel?.let { viewModel ->
            viewModel.mDemandSongList.observe(this@MainActivity) {
                Log.d(TAG, "mDemandSongList size: ${it.size}")
                if (it.isNotEmpty()) {
                    mBinding.tvDemandCount.visibility = View.VISIBLE
                    if (it.size > 99) {
                        mBinding.tvDemandCount.text = getString(R.string.number_99_more)
                    } else {
                        mBinding.tvDemandCount.text = (it.size).toString()
                    }
                } else {
                    mBinding.tvDemandCount.visibility = View.GONE
                }
                mViewModel?.getCurrentAndNextSongInfo()
            }
            viewModel.mSearchResultBean.observe(this@MainActivity) {
                it.onSuccess { value, operation ->
                    Log.d(TAG, "onSuccess")
                    if (value == null) {//没有搜索到数据
                        Log.d(TAG, "没有搜索到内容")
                        VrManager.sendKSongPlayAction(ret = VrManager.RET_FAIELD)
                    } else {
                        val songs = value.songs
                        if (songs.isNullOrEmpty()) {//没有搜索到数据
                            Log.d(TAG, "没有搜索到内容")
                            VrManager.sendKSongPlayAction(ret = VrManager.RET_FAIELD)
                        } else {
                            vrSongInfo = songs[0]
                            val fragment = this.supportFragmentManager.findFragmentByTag(
                                VrKSongPlayDialogFragment::class.simpleName
                            )
                            val message = VrManager.getSearchMessage(vrSongInfo?.singer_name, vrSongInfo?.song_name)
                            if (fragment == null) {
                                Log.d(TAG, "show Dialog song ${vrSongInfo.toString()}")
                                val vrKSongPlayDialogFragment = VrKSongPlayDialogFragment()
                                vrKSongPlayDialogFragment.setMessage(message)
                                vrKSongPlayDialogFragment.show(
                                    supportFragmentManager,
                                    VrKSongPlayDialogFragment::class.simpleName
                                )
                                vrKSongPlayDialogFragment.setOnConfirmListener {
                                    Log.d(TAG, "dialog confirm $vrSongInfo")
                                    vrSongInfo?.let { songInfo ->
                                        VrManager.sendKSongPlayAction(
                                            songInfo.singer_name,
                                            songInfo.song_name,
                                            VrManager.RET_SUCCESS
                                        )
                                        KaraokePlayerManager.playSong(this, vrSongInfo, needReplay = true)
                                    }
                                }
                                vrKSongPlayDialogFragment.setOnCancelListener {
                                    Log.d(TAG, "dialog cancel")
                                    VrManager.sendKSongPlayAction(ret = VrManager.RET_CANCEL)
                                }
                            }
                            vrSongInfo?.let { songInfo ->
                                VrManager.sendKSongPlayAction(
                                    songInfo.singer_name,
                                    songInfo.song_name,
                                    VrManager.RET_ASK
                                )
                            }
                        }
                    }

                }.onFailure { resultCode, operation ->
                    Log.d(TAG, "onFailure")
                    VrManager.sendKSongPlayAction(ret = VrManager.RET_NO_NETWORK)
                    ToastUtils.showLong(R.string.no_network_hint)
                }
            }
            viewModel.goSongInfoValue.observe(this@MainActivity) {
                val requestAudioFocus = AudioFocusHelper.requestAudioFocus()
                if (!requestAudioFocus) {
                    Log.d(TAG, "playSong requestAudioFocus false")
                    return@observe
                }
                songPlayController?.playSong(it.third, it.first, it.second)
            }
            viewModel.isPermissionChange.observe(this@MainActivity) {
                findNavController(R.id.nav_host_fragment_activity_main).currentDestination?.id?.let { id ->
                    Log.d(TAG, "currentDestination id = $id")
                    if (id == R.id.navigation_login || id == R.id.navigation_agree_privacy) {
                        Log.d(TAG, "currentDestination id is login or privacy")
                        return@observe
                    }
                }
                val playState = KaraokePlayerManager.getPlayer().playState
                Log.d(TAG, "isPermissionChange playState = $playState")
                if (playState == VideoState.STATE_START
                    || playState == VideoState.STATE_PLAYING
                    || playState == VideoState.STATE_PAUSE
                ) {
                    PermissionHelper.checkPermission {}
                }

            }
            viewModel.lyricStrLiveData.observe(this@MainActivity) {
                if (TextUtils.isEmpty(it)) {
                    mBinding.tvLyric.text = getString(R.string.main_lyric_default)
                } else {
                    mBinding.tvLyric.text = it
                }
            }
        }
        KaraokeConsole.playState.observe(this@MainActivity) { playState ->
            Log.d(TAG, "playState = $playState")
            val isPlay = playState == VideoState.STATE_PLAYING
            VrManager.sendKSongStatus(isPlay)
            mBinding.ivMainPlay.setImageResource(
                if (isPlay) {
                    R.drawable.selector_pause
                } else {
                    R.drawable.selector_play
                }
            )
        }
        // 使用 SharedFlow 替代 AutoCleanLiveData，避免粘性问题
        lifecycleScope.launch {
            KaraokeConsole.preLoadProgress.collect { progress ->
                Log.d(TAG, "preLoadProgress $progress")
                if (progress == 100) {
                    mViewModel?.getNextPlaySongInfo()
                }
            }
        }
        MainViewModel.tokenErrorStatusLiveData.observe(this) {
            if (NavigationUtils.isCurrentLoginFragment()) {
                return@observe
            }
            when (it) {
                ERROR_CODE_TOKEN_EXPIRE -> {
                    showToast(R.string.error_code_token_expire_hint)
                    AccessTokenManager.logout()
                    NavigationUtils.navigateSafely(
                        findNavController(R.id.nav_host_fragment_activity_main),
                        R.id.navigation_login
                    )
                }

                ERROR_CODE_OTHER_LOGIN -> {
                    showToast(R.string.other_login_hint)
                    AccessTokenManager.logout()
                    NavigationUtils.navigateSafely(
                        findNavController(R.id.nav_host_fragment_activity_main),
                        R.id.navigation_login
                    )
                }
            }
        }
        CarManager.mCurrentGear.observeForever {
            Log.d(TAG, "档位发生变化 mCurrentGear: $it")
            handleWalkLimit()
        }
        CarManager.mCurrentWalkRegulationState.observeForever {
            Log.d(TAG, "行车规制状态变化 mCurrentWalkRegulationState: $it")
            handleWalkLimit()
        }
        KaraokePlayerManager.showLimitDialog.observeForever { showDialog ->
            if (showDialog) {
                showWalkingRegulationDialog()
            }
        }
        KaraokePlayerManager.showPlayPage.observeForever { showPage ->
            if (!showPage && songPlayController?.isPlayViewShow() == true) {
                songPlayController?.hide()
            } else if (showPage) {
                songPlayController?.show()
            }
        }
        PhoneStatusManager.phoneCallState.observeForever { isCall ->
            songPlayController?.handlePhoneCall(isCall)

        }

        mViewModel?.currentAndNextSongInfo?.observe(this) {
            Log.d(
                TAG,
                "currentAndNextSongInfo = first: ${it.first?.songInfo?.song_name} second: ${it.second?.songInfo?.song_name}"
            )
            val isPlayAndNextState = it.first != null || it.second != null
            Log.d(TAG, "isPlayAndNextState = $isPlayAndNextState")
            mBinding.ivMainPlay.isEnabled = isPlayAndNextState
            mBinding.ivNext.isEnabled = isPlayAndNextState
        }
    }

    /**
     * 处理行车规制
     */
    private fun handleWalkLimit() {
        val isSongPlayFragment = songPlayController?.isPlayViewShow() ?: false
        Log.d(
            TAG,
            "handleWalkLimit = $isSongPlayFragment == CarManager.isLimit = ${CarManager.isLimit()}" +
                    " == isInForeground = ${isInForeground}"
        )
        if (isSongPlayFragment && CarManager.isLimit() && isInForeground) {
            // 暂停播放
            if (KaraokePlayerManager.isPlaying()) {
                KaraokePlayerManager.pause()
            }
            showWalkingRegulationDialog()
        }
    }

    /**
     * 展示行车规制弹框
     */
    private fun showWalkingRegulationDialog() {
        val vehicleVideoDialog = VehicleVideoDialogFragment()
        // 检查 VehicleVideoDialogFragment 是否已经显示
        val fragment = supportFragmentManager.findFragmentByTag("VehicleVideoDialogFragment")
        if (fragment == null) {
            // 弹出 ScoreDialogFragment
            vehicleVideoDialog.show(supportFragmentManager, "VehicleVideoDialogFragment")
        }
    }

    override fun ActivityMainBinding.initView() {
        mBinding.ivMainPlay.setOnSingleClickListener {
            Log.d(TAG, "ivMainPlay onClick")
            if (PhoneStatusManager.isInCall()) return@setOnSingleClickListener
            if (mViewModel?.isPlaying() == true) {
                Log.d(TAG, "ivMainPlay onClick: 暂停播放")
                KaraokePlayerManager.getPlayer().pause()
            } else {
                Log.d(TAG, "ivMainPlay onClick: 播放")
                mViewModel?.handlePlay()
            }
        }
        mBinding.ivNext.setOnSingleClickListener {
            val currentAndNextSongInfo = mViewModel?.currentAndNextSongInfo?.value
            val nextSongInfo = currentAndNextSongInfo?.second
            if (nextSongInfo != null) {
                Log.d(TAG, "ivNext onClick: 有下一曲")
                KaraokePlayerManager.playNextSong()
            } else {
                Log.d(TAG, "ivNext onClick: 没有下一曲")
                currentAndNextSongInfo?.first?.let { currentSongInfo ->
                    //防止最后一首未加载完，进调用stop并不能停止，还需把加载中的歌曲也取消掉
                    PlayerManager.getPlayPreloader().cancelAll()
                    KaraokePlayerManager.stop()
                    // 没有下一曲，只有当前播放的歌曲，点击下一曲，就把当前的歌曲添加到已唱列表
                    PlayListManager.updateSungList(currentSongInfo.songInfo.song_id)
                }
            }

        }
        mBinding.ivList.setOnSingleClickListener {
            Log.d(TAG, "ivList onClick: 播放列表")
            showDemandListDialog()
        }
        mBinding.llPlayControl.setOnSingleClickListener {
            Log.d(TAG, "llPlayControl onClick:")
            if (PhoneStatusManager.isInCall()) return@setOnSingleClickListener
            val player = KaraokePlayerManager.getPlayer()
            val playState = player.playState
            Log.d(TAG, "llPlayControl playState: ${player.playState}")
            if (playState == VideoState.STATE_PLAYING || playState == VideoState.STATE_PAUSE) {
                KaraokePlayerManager.playSong(this@MainActivity, KaraokeConsole.currSongInfo)
            } else {
                ioLaunch {
                    val currSongInfo = DbManager.getDemandSongInfoDao().getPlayingSongInfo()?.songInfo
                    if (currSongInfo == null) {
                        val nextPlaySongInfo = PlayListManager.getNextPlaySongInfo()
                        if (nextPlaySongInfo != null) {
                            KaraokePlayerManager.playSong(this@MainActivity, nextPlaySongInfo.songInfo)
                        } else {
                            Log.d(TAG, "onClick: 没有下一曲")
                        }
                    } else {
                        Log.d(TAG, "llPlayControl playState: ${player.playState}")
                        if (player.playState == VideoState.STATE_IDLE || player.playState == VideoState.STATE_ENDED) {
                            KaraokePlayerManager.playSong(this@MainActivity, currSongInfo)
                        } else {
                            KaraokePlayerManager.playSong(this@MainActivity, null)
                        }
                    }
                }

            }
        }

        if (mViewModel?.isAgreePrivacy() == true) {
            Log.d(TAG, "mViewModel?.isAgreePrivacy() == true")
            if (!AccessTokenManager.isLogin()) {
                Log.d(TAG, "!AccessTokenManager.isLogin()")
                NavigationUtils.navigateSafely(
                    findNavController(R.id.nav_host_fragment_activity_main),
                    R.id.action_to_login
                )
            } else {
                Log.d(TAG, "AccessTokenManager.isLogin()")
                NavigationUtils.navigateSafely(
                    findNavController(R.id.nav_host_fragment_activity_main),
                    R.id.navigation_home
                )
            }
        } else {
            Log.d(TAG, "mViewModel?.isAgreePrivacy() == false")
            NavigationUtils.navigateSafely(
                findNavController(R.id.nav_host_fragment_activity_main),
                R.id.navigation_agree_privacy
            )
        }
        // 欢迎页
        LingOsDialogUtils.showPrivacyAgreement(applicationContext, object :
            LingOsDialogUtils.IAgreementListener {
            override fun onAgreeState(agree: Boolean) {
                Log.d(TAG, "onAgreeState $agree")
                if (!agree) {
                    finish()
                }
            }
        })

        supportFragmentManager.findFragmentById(R.id.nav_host_fragment_activity_main)
            ?.let { navHostFragment ->
                if (navHostFragment is NavHostFragment) {
                    navHostFragment.childFragmentManager.registerFragmentLifecycleCallbacks(object :
                        FragmentManager.FragmentLifecycleCallbacks() {

                        override fun onFragmentPreCreated(
                            fm: FragmentManager,
                            f: Fragment,
                            savedInstanceState: Bundle?
                        ) {
                        }

                        override fun onFragmentViewDestroyed(fm: FragmentManager, f: Fragment) {
                        }

                        override fun onFragmentResumed(fm: FragmentManager, f: Fragment) {
                            super.onFragmentResumed(fm, f)
                            Log.d(TAG, "onFragmentStarted: ${f.javaClass.simpleName}")
                            if (f is DialogFragment) {
                                return
                            }
                            // 下方播放器条是否展示
                            val playControlVisible: Boolean = when (f) {
                                is AgreePrivacyFragment,
                                is LoginFragment,
                                is SettingsFragment,
                                is MicConnectionGuideFragment,
                                is ProtocolHomeFragment,
                                is ProtocolWebFragment -> false

                                else -> true
                            }
                            showHidePlayControl(playControlVisible)
                        }

                    }, false)
                }
            }

    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        handlePowerState()
        intent?.let {
            val data = it.getStringExtra(VrManager.VR_KSONG_PLAY)
            data?.let {
                vrKsongData = data
                Log.d(TAG, "onNewIntent $vrKsongData")
                val jsonObject = JSONObject(vrKsongData)
                if (!jsonObject.has("e_device_onoff")) {
                    return
                }
                val deviceOnOff = jsonObject["e_device_onoff"].toString()
                Log.d(TAG, "deviceOnOff $deviceOnOff")
                val fragment = this.supportFragmentManager.findFragmentByTag(
                    VrKSongPlayDialogFragment::class.simpleName
                )
                var vrKSongPlayDialogFragment: VrKSongPlayDialogFragment? = null
                if (fragment != null) {
                    vrKSongPlayDialogFragment = fragment as VrKSongPlayDialogFragment
                }
                when (deviceOnOff) {
                    VrManager.DEVICE_ON -> {
                        if (mViewModel?.isAgreePrivacy() == true) {
                            if (!AccessTokenManager.isLogin()) {
                                Log.d(TAG, "Vr 登录")
                                if (findNavController(R.id.nav_host_fragment_activity_main).currentDestination?.id != R.id.navigation_login) {
                                    Log.d(TAG, "OnNewIntent 不在登录页面")
                                    NavigationUtils.navigateSafely(
                                        findNavController(R.id.nav_host_fragment_activity_main),
                                        R.id.action_to_login
                                    )
                                } else {
                                    Log.d(TAG, "OnNewIntent 当前登录页面")
                                }
                                VrManager.sendKSongPlayAction(ret = VrManager.RET_NO_LOGIN)
                            } else if (PermissionHelper.isHaveMicrophonePermission()) {
                                Log.d(TAG, "Vr 搜索")
                                if (vrKSongPlayDialogFragment?.dialog?.isShowing == true) {
                                    vrKSongPlayDialogFragment.cancel()
                                }
                                if (!NetworkStateClient.isConnected()) {
                                    Log.d(TAG, "OnNewIntent 无网络")
                                    ToastUtils.showLong(R.string.no_network_hint)
                                } else {
                                    mViewModel?.kSongSearch(VrManager.getSearchWord(vrKsongData))
                                }
                            } else {
                                PermissionHelper.checkPermission { }
                                VrManager.sendKSongPlayAction(ret = VrManager.RET_NO_AGREE)
                            }
                        } else {
                            Log.d(TAG, "Vr 隐私协议")
                            if (findNavController(R.id.nav_host_fragment_activity_main).currentDestination?.id != R.id.navigation_agree_privacy) {
                                Log.d(TAG, "OnNewIntent 不在隐私页面")
                                NavigationUtils.navigateSafely(
                                    findNavController(R.id.nav_host_fragment_activity_main),
                                    R.id.navigation_agree_privacy
                                )
                            } else {
                                Log.d(TAG, "OnNewIntent 当前隐私页面")
                            }
                            VrManager.sendKSongPlayAction(ret = VrManager.RET_NO_AGREE)
                        }
                    }

                    VrManager.DEVICE_CONFIRM -> {
                        if (vrKSongPlayDialogFragment?.dialog?.isShowing == true) {
                            vrKSongPlayDialogFragment.confirm()
                        } else {
                            Log.d(TAG, "vrKSongPlayDialogFragment isShowing = false")
                        }
                    }

                    VrManager.DEVICE_CANCEL -> {
                        if (vrKSongPlayDialogFragment?.dialog?.isShowing == true) {
                            vrKSongPlayDialogFragment.cancel()
                        } else {
                            Log.d(TAG, "vrKSongPlayDialogFragment isShowing = false")
                        }
                    }

                    else -> {}
                }
            }

        }
    }

    /**
     * 处理下电再上电页面跳转逻辑
     */
    private fun handlePowerState() {
        if (needBackHome) {
            needBackHome = false
            if (mViewModel?.isAgreePrivacy() == true && AccessTokenManager.isLogin()) {
                val dialogFragmentShowing =
                    songPlayController?.isDialogFragmentShowing(this@MainActivity)
                if (songPlayController?.isPlayViewShow() == true) {
                    songPlayController?.hide()
                }
                if (dialogFragmentShowing == true) {
                    // 关闭所有显示的 DialogFragment
                    val fragments = supportFragmentManager.fragments
                    for (fragment in fragments) {
                        if (fragment is DialogFragment && fragment.dialog != null && fragment.dialog!!.isShowing) {
                            fragment.dismiss()
                        }
                    }
                }
                NavigationUtils.getNavController()?.popBackStack(R.id.navigation_home, true)
                mViewModel?.changePageLiveData?.postValue(0)
            } else {
                if (findNavController(R.id.nav_host_fragment_activity_main).currentDestination?.id == R.id.navigation_fragment_protocol) {
                    Log.d(TAG, "下电前在隐私协议页面，下电后回到首页")
                    NavigationUtils.getNavController()?.popBackStack()
                }
            }
        }
    }

    /**
     * 获取SongPlayController是否被激活（不管是否被遮挡）
     * 用于VipPaymentFragment判断是否需要委托MainActivity处理返回事件
     * @return true if SongPlayController is activated, false otherwise
     */
    fun isSongPlayControllerShowing(): Boolean {
        // 直接使用SongPlayController的isPlayViewShow()方法
        // 虽然这个方法名字有误导性，但它确实能准确反映SongPlayController是否被激活
        // 因为show()和hide()方法会正确设置isViewHidden状态
        return songPlayController?.isPlayViewShow() == true
    }

    /**
     * 是否展示底部播放器
     * 如果是播放页面就隐藏播放器，否则展示播放器
     */
    private fun showHidePlayControl(isPlayControlShow: Boolean) {
        Log.d(
            TAG,
            "showHidePlayControl: isPlayControlShow $isPlayControlShow KaraokeConsole.playState: ${KaraokeConsole.playState.value}"
        )
        val playControlVisibility =
            if (isPlayControlShow) {
                View.VISIBLE
            } else {
                View.GONE
            }
        mBinding.llPlayControl.visibility = playControlVisibility
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "onDestroy start")

        try {
            // 清理导航控制器
            NavigationUtils.clearNavController()

            // 取消车机状态监听
            CarManager.unregisterCarPowerStateListener(carPowerStateListener)

            // 清理歌曲播放控制器
            songPlayController?.onDestroyView()
            songPlayController = null

            // 停止播放器并清理资源
            KaraokePlayerManager.cleanup()

            // 清理线程池资源
            cleanupThreadPools()

            // 解注册广播接收器
            unregisterReceiver(mReceiver)

            Log.d(TAG, "onDestroy completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error during onDestroy", e)
        }
    }



    /**
     * 车机关闭准备，提前清理资源避免ANR
     */
    private fun prepareForShutdown() {
        Log.d(TAG, "Preparing for shutdown")
        try {
            // 在后台线程中执行清理操作，避免阻塞主线程
            ioLaunch {
                // 停止播放器
                KaraokePlayerManager.stop()

                // 释放音频焦点
                AudioFocusHelper.abandonAudioFocus()

                // 清理线程池（使用shutdownNow立即停止）
                com.autoai.baseline.support.autoinflater.AutoInflaterThreadPoolUtil.getInstance().shutdownNow()

                Log.d(TAG, "Shutdown preparation completed")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error during shutdown preparation", e)
        }
    }

    /**
     * 清理线程池资源，避免阻塞应用关闭
     */
    private fun cleanupThreadPools() {
        try {
            // 清理AutoInflater线程池
            com.autoai.baseline.support.autoinflater.AutoInflaterThreadPoolUtil.getInstance().shutdown()

            Log.d(TAG, "Thread pools cleanup completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error during thread pools cleanup", e)
        }
    }

    companion object {
        private const val TAG = "MainActivity"
    }

    override fun networkConnectChange(isConnected: Boolean) {
        if (isConnected) {
            VipAndLimitManager.getVipInfo { }
        }
    }

    /**
     * 播放列表弹框
     */
    private fun showDemandListDialog() {
        val songListDialogFragment = SongListDialogFragment()
        // 检查 OpenScoreDialog 是否已经显示
        val fragment = supportFragmentManager.findFragmentByTag("SongListDialogFragment")
        if (fragment == null) {
            // 弹出 ScoreDialogFragment
            songListDialogFragment.show(supportFragmentManager, "SongListDialogFragment")
        }
    }

    fun isShowPlayPage(): Boolean {
        return songPlayController?.isPlayViewShow() == true
    }
}